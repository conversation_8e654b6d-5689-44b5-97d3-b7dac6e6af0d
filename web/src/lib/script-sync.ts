import { extensionBridge } from './extension-bridge';
import { BaseScriptSyncManager, ScriptStorage, Script } from '@the-agent/shared';

/**
 * Web-specific storage implementation using ExtensionBridge with localStorage fallback
 */
class WebScriptStorage implements ScriptStorage {
  async saveScript(script: <PERSON>rip<PERSON>): Promise<void> {
    if (!script.miniapp_id) {
      throw new Error('miniapp_id is required for script operations');
    }

    try {
      // Check if extension is available first
      const isExtensionAvailable = await extensionBridge.isExtensionAvailable();

      if (isExtensionAvailable) {
        await extensionBridge.saveScript({
          messageId: script.message_id,
          code: script.displayCode,
          miniappId: parseInt(script.miniapp_id),
          name: script.name,
          version: script.version || 1,
        });
      } else {
        console.warn('Extension not available, script saved locally only');
        // Store in localStorage as fallback
        this.saveToLocalStorage(script);
      }
    } catch (error) {
      console.error('Failed to save script via extension:', error);
      // Fallback to localStorage
      this.saveToLocalStorage(script);
    }
  }

  async deleteScript(messageId: string, miniappId?: string): Promise<void> {
    try {
      // Check if extension is available first
      const isExtensionAvailable = await extensionBridge.isExtensionAvailable();

      if (isExtensionAvailable && miniappId) {
        await extensionBridge.deleteScript(parseInt(miniappId));
      } else {
        console.warn(
          'Extension not available or miniappId missing, removing from localStorage only'
        );
      }
    } catch (error) {
      console.error('Failed to delete script via extension:', error);
    }

    // Always remove from localStorage as well
    this.removeFromLocalStorage(messageId);
  }

  async getScript(messageId: string, miniappId?: string): Promise<Script | null> {
    if (!miniappId) {
      console.warn('miniappId not provided, trying localStorage fallback');
      return this.getFromLocalStorage(messageId);
    }

    try {
      // Check if extension is available first
      const isExtensionAvailable = await extensionBridge.isExtensionAvailable();

      if (isExtensionAvailable) {
        const scriptData = await extensionBridge.getScript(parseInt(miniappId));

        if (scriptData) {
          return {
            message_id: messageId,
            displayCode: scriptData.code,
            updated_at: scriptData.updated_at,
            miniapp_id: miniappId,
            version: scriptData.version,
          };
        }
      }

      // Fallback to localStorage
      return this.getFromLocalStorage(messageId);
    } catch (error) {
      console.error('Failed to get script via extension:', error);
      // Fallback to localStorage
      return this.getFromLocalStorage(messageId);
    }
  }

  async getScriptFromMiniapp(miniappId: number): Promise<Script | null> {
    try {
      const scriptData = await extensionBridge.getScript(miniappId);

      if (!scriptData) {
        return null;
      }

      return {
        message_id: `miniapp-${miniappId}`,
        displayCode: scriptData.code,
        updated_at: scriptData.updated_at,
        miniapp_id: miniappId.toString(),
        version: scriptData.version,
      };
    } catch (error) {
      console.error('Failed to get script from miniapp:', error);
      return null;
    }
  }

  private saveToLocalStorage(script: Script): void {
    try {
      const key = `script_${script.message_id}`;
      localStorage.setItem(key, JSON.stringify(script));
    } catch (error) {
      console.error('Failed to save script to localStorage:', error);
    }
  }

  private getFromLocalStorage(messageId: string): Script | null {
    try {
      const key = `script_${messageId}`;
      const stored = localStorage.getItem(key);
      if (stored) {
        return JSON.parse(stored) as Script;
      }
    } catch (error) {
      console.error('Failed to get script from localStorage:', error);
    }
    return null;
  }

  private removeFromLocalStorage(messageId: string): void {
    try {
      const key = `script_${messageId}`;
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Failed to remove script from localStorage:', error);
    }
  }
}

/**
 * Web-specific ScriptSyncManager implementation
 */
class ScriptSyncManager extends BaseScriptSyncManager {
  constructor() {
    super(new WebScriptStorage());
  }
}

// Singleton instance
export const scriptSyncManager = new ScriptSyncManager();
