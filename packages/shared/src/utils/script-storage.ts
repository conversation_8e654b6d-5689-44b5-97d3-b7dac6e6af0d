import { Script, ScriptSyncEvent } from '../types/api';

/**
 * Abstract interface for script storage operations
 * Different platforms (web, extension) can implement their own storage strategies
 */
export interface ScriptStorage {
  /**
   * Save a script to storage
   */
  saveScript(script: Script): Promise<void>;

  /**
   * Delete a script from storage
   */
  deleteScript(messageId: string, miniappId?: string): Promise<void>;

  /**
   * Get a script from storage
   */
  getScript(messageId: string, miniappId?: string): Promise<Script | null>;

  /**
   * Get script from miniapp developing field
   */
  getScriptFromMiniapp(miniappId: number): Promise<Script | null>;
}

/**
 * Abstract base class for script synchronization
 * Handles cross-context communication and event broadcasting
 */
export abstract class BaseScriptSyncManager {
  protected channel: BroadcastChannel | null = null;
  protected listeners: Set<(event: ScriptSyncEvent) => void> = new Set();
  protected storage: ScriptStorage;

  constructor(storage: ScriptStorage) {
    this.storage = storage;

    // Try to create BroadcastChannel for cross-context communication
    try {
      this.channel = new BroadcastChannel('script-sync');
      this.channel.addEventListener('message', this.handleBroadcastMessage.bind(this));
    } catch (error) {
      console.warn('BroadcastChannel not available, using fallback sync', error);
    }
  }

  private handleBroadcastMessage(event: MessageEvent<ScriptSyncEvent>) {
    this.notifyListeners(event.data);
  }

  private notifyListeners(event: ScriptSyncEvent) {
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in sync listener:', error);
      }
    });
  }

  /**
   * Save script and notify other contexts
   */
  async saveScript(script: Script): Promise<void> {
    if (!script.miniapp_id) {
      throw new Error('miniapp_id is required for script operations');
    }

    script.updated_at = Date.now();

    try {
      await this.storage.saveScript(script);

      const event: ScriptSyncEvent = {
        type: 'script-updated',
        messageId: script.message_id,
        script,
        timestamp: Date.now(),
      };

      this.broadcastEvent(event);
    } catch (error) {
      console.error('Failed to save script:', error);
      throw error;
    }
  }

  /**
   * Delete script and notify other contexts
   */
  async deleteScript(messageId: string, miniappId?: string): Promise<void> {
    try {
      await this.storage.deleteScript(messageId, miniappId);

      const event: ScriptSyncEvent = {
        type: 'script-deleted',
        messageId,
        timestamp: Date.now(),
      };

      this.broadcastEvent(event);
    } catch (error) {
      console.error('Failed to delete script:', error);
      throw error;
    }
  }

  /**
   * Get script from storage
   */
  async getScript(messageId: string, miniappId?: string): Promise<Script | null> {
    try {
      return await this.storage.getScript(messageId, miniappId);
    } catch (error) {
      console.error('Failed to get script:', error);
      return null;
    }
  }

  /**
   * Get script from miniapp developing field
   */
  async getScriptFromMiniapp(miniappId: number): Promise<Script | null> {
    try {
      return await this.storage.getScriptFromMiniapp(miniappId);
    } catch (error) {
      console.error('Failed to get script from miniapp:', error);
      return null;
    }
  }

  protected broadcastEvent(event: ScriptSyncEvent) {
    if (this.channel) {
      try {
        this.channel.postMessage(event);
      } catch (error) {
        console.warn('Failed to broadcast sync event:', error);
      }
    }
  }

  /**
   * Listen for sync events from other contexts
   */
  addSyncListener(listener: (event: ScriptSyncEvent) => void) {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * Cleanup resources
   */
  destroy() {
    if (this.channel) {
      this.channel.close();
    }
    this.listeners.clear();
  }
}
