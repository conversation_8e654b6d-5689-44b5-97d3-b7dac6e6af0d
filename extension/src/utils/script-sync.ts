import { db } from '~/storages/indexdb';
import { BaseScriptSyncManager, ScriptStorage, Script, Developing } from '@the-agent/shared';

/**
 * Extension-specific storage implementation using IndexedDB
 */
class ExtensionScriptStorage implements ScriptStorage {
  async saveScript(script: <PERSON>rip<PERSON>): Promise<void> {
    if (!script.miniapp_id) {
      throw new Error('miniapp_id is required for script operations');
    }

    const developing: Developing = {
      code: script.displayCode,
      version: script.version || 1,
      updated_at: script.updated_at,
    };

    await db.updateMiniapp(parseInt(script.miniapp_id), { developing });
  }

  async deleteScript(miniappId?: string): Promise<void> {
    if (!miniappId) {
      throw new Error('miniappId is required for script deletion');
    }

    await db.updateMiniapp(parseInt(miniappId), { developing: null });
  }

  async getScript(messageId: string, miniappId?: string): Promise<Script | null> {
    if (!miniappId) {
      console.warn('miniappId not provided, cannot fetch script');
      return null;
    }

    const miniapp = await db.getMiniapp(parseInt(miniappId));

    if (!miniapp?.developing) {
      return null;
    }

    return {
      message_id: messageId,
      displayCode: miniapp.developing.code,
      updated_at: miniapp.developing.updated_at,
      miniapp_id: miniappId,
      version: miniapp.developing.version,
    };
  }

  async getScriptFromMiniapp(miniappId: number): Promise<Script | null> {
    const miniapp = await db.getMiniapp(miniappId);

    if (!miniapp?.developing) {
      return null;
    }

    return {
      message_id: `miniapp-${miniappId}`,
      displayCode: miniapp.developing.code,
      updated_at: miniapp.developing.updated_at,
      miniapp_id: miniappId.toString(),
      version: miniapp.developing.version,
    };
  }
}

/**
 * Extension-specific ScriptSyncManager implementation
 */
class ScriptSyncManager extends BaseScriptSyncManager {
  constructor() {
    super(new ExtensionScriptStorage());
  }
}

// Singleton instance
export const scriptSyncManager = new ScriptSyncManager();
